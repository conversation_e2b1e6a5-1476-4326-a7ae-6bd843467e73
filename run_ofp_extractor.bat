@echo off
REM OFP Extractor Windows Batch Script
REM This script helps Windows users run the OFP extractor easily

echo ================================================
echo OFP Firmware Extractor
echo ================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if the main script exists
if not exist "ofp_extractor.py" (
    echo Error: ofp_extractor.py not found in current directory
    echo Please make sure you're running this script from the correct folder
    echo.
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo Checking dependencies...
python -c "import Crypto" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required dependencies...
    pip install pycryptodome
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        echo Please run: pip install pycryptodome
        echo.
        pause
        exit /b 1
    )
)

echo Dependencies OK
echo.

REM Show usage options
echo Usage Options:
echo 1. Extract a single OFP file
echo 2. List contents of an OFP file
echo 3. Batch process multiple OFP files
echo 4. Show help
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto extract_single
if "%choice%"=="2" goto list_contents
if "%choice%"=="3" goto batch_process
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
goto :eof

:extract_single
echo.
set /p input_file="Enter path to OFP file: "
set /p output_dir="Enter output directory: "
echo.
echo Extracting %input_file% to %output_dir%...
python ofp_extractor.py -i "%input_file%" -o "%output_dir%" --verbose
goto end

:list_contents
echo.
set /p input_file="Enter path to OFP file: "
echo.
echo Listing contents of %input_file%...
python ofp_extractor.py -i "%input_file%" --list-only --verbose
goto end

:batch_process
echo.
set /p input_dir="Enter directory containing OFP files: "
set /p output_dir="Enter output directory: "
echo.
echo Processing all OFP files in %input_dir%...
python ofp_extractor.py --batch "%input_dir%" -o "%output_dir%" --verbose
goto end

:show_help
echo.
python ofp_extractor.py --help
goto end

:end
echo.
echo Operation completed.
pause

:exit
echo Goodbye!
