#!/usr/bin/env python3
"""
Example usage of the OFP Extractor
Demonstrates how to use the OFP extractor programmatically
"""

import os
import sys
import tempfile
import zipfile

# Add the current directory to Python path to import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ofp_extractor import OFPExtractor, OFPFormat

def create_sample_zip_ofp(filepath):
    """Create a sample ZIP-format OFP file for demonstration."""
    with zipfile.ZipFile(filepath, 'w') as zf:
        # Add some sample files that might be in a firmware
        zf.writestr("boot.img", b"Sample boot image data")
        zf.writestr("system.img", b"Sample system image data")
        zf.writestr("recovery.img", b"Sample recovery image data")
        zf.writestr("META-INF/com/android/metadata", "Sample metadata")
        zf.writestr("firmware-info.txt", "Sample firmware information")
    
    print(f"Created sample ZIP OFP file: {filepath}")

def demonstrate_programmatic_usage():
    """Demonstrate using the OFP extractor programmatically."""
    print("=" * 60)
    print("OFP Extractor - Programmatic Usage Example")
    print("=" * 60)
    
    # Create a temporary directory for our demo
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create sample OFP file
        sample_ofp = os.path.join(temp_dir, "sample_firmware.ofp")
        create_sample_zip_ofp(sample_ofp)
        
        # Initialize the extractor
        print("\n1. Initializing OFP Extractor...")
        extractor = OFPExtractor(verbose=True)
        
        # Detect format
        print("\n2. Detecting OFP format...")
        format_type = extractor.detect_ofp_format(sample_ofp)
        print(f"Detected format: {format_type.value}")
        
        # List contents without extracting
        print("\n3. Listing contents (list-only mode)...")
        output_dir = os.path.join(temp_dir, "extracted")
        success = extractor.extract_ofp(sample_ofp, output_dir, list_only=True)
        print(f"List operation success: {success}")
        
        # Extract files
        print("\n4. Extracting files...")
        success = extractor.extract_ofp(sample_ofp, output_dir, list_only=False)
        print(f"Extraction success: {success}")
        
        # Show extracted files
        if success and os.path.exists(output_dir):
            print("\n5. Extracted files:")
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, output_dir)
                    file_size = os.path.getsize(file_path)
                    print(f"  {rel_path} ({file_size} bytes)")
        
        print("\n" + "=" * 60)
        print("Demonstration completed successfully!")
        print("=" * 60)

def demonstrate_command_line_usage():
    """Show examples of command-line usage."""
    print("\n" + "=" * 60)
    print("Command-Line Usage Examples")
    print("=" * 60)
    
    examples = [
        {
            "description": "Extract a single OFP file",
            "command": "python ofp_extractor.py -i firmware.ofp -o extracted/"
        },
        {
            "description": "List contents without extracting",
            "command": "python ofp_extractor.py -i firmware.ofp --list-only"
        },
        {
            "description": "Extract with verbose output",
            "command": "python ofp_extractor.py -i firmware.ofp -o extracted/ --verbose"
        },
        {
            "description": "Batch process multiple files",
            "command": "python ofp_extractor.py --batch firmware_dir/ -o extracted_dir/"
        },
        {
            "description": "Show help",
            "command": "python ofp_extractor.py --help"
        },
        {
            "description": "Show version",
            "command": "python ofp_extractor.py --version"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   {example['command']}")
    
    print("\n" + "=" * 60)

def show_supported_formats():
    """Show information about supported formats."""
    print("\n" + "=" * 60)
    print("Supported OFP Formats")
    print("=" * 60)
    
    formats = [
        {
            "name": "ZIP Format",
            "description": "Password-protected ZIP files",
            "features": [
                "Automatic password detection",
                "Standard ZIP extraction",
                "Fallback for unknown passwords"
            ]
        },
        {
            "name": "Qualcomm (QC) Format",
            "description": "Qualcomm chipset firmware",
            "features": [
                "Multiple encryption key versions",
                "XML metadata parsing",
                "File integrity verification",
                "AES CFB decryption"
            ]
        },
        {
            "name": "MediaTek (MTK) Format",
            "description": "MediaTek chipset firmware",
            "features": [
                "Header-based file tables",
                "Project information extraction",
                "Multiple encryption algorithms",
                "Binary header parsing"
            ]
        }
    ]
    
    for fmt in formats:
        print(f"\n{fmt['name']}:")
        print(f"  Description: {fmt['description']}")
        print("  Features:")
        for feature in fmt['features']:
            print(f"    • {feature}")

def main():
    """Main example function."""
    try:
        # Show supported formats
        show_supported_formats()
        
        # Demonstrate programmatic usage
        demonstrate_programmatic_usage()
        
        # Show command-line examples
        demonstrate_command_line_usage()
        
        print("\nFor more information, see README.md or run:")
        print("python ofp_extractor.py --help")
        
        return 0
        
    except Exception as e:
        print(f"Error in demonstration: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
