#!/bin/bash
# OFP Extractor Linux/macOS Shell Script
# This script helps Unix users run the OFP extractor easily

echo "================================================"
echo "OFP Firmware Extractor"
echo "================================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8 or higher"
    exit 1
fi

# Check if the main script exists
if [ ! -f "ofp_extractor.py" ]; then
    echo "Error: ofp_extractor.py not found in current directory"
    echo "Please make sure you're running this script from the correct folder"
    exit 1
fi

# Check if dependencies are installed
echo "Checking dependencies..."
if ! python3 -c "import Crypto" &> /dev/null; then
    echo "Installing required dependencies..."
    pip3 install pycryptodome
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        echo "Please run: pip3 install pycryptodome"
        exit 1
    fi
fi

echo "Dependencies OK"
echo

# Show usage options
echo "Usage Options:"
echo "1. Extract a single OFP file"
echo "2. List contents of an OFP file"
echo "3. Batch process multiple OFP files"
echo "4. Show help"
echo "5. Exit"
echo

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo
        read -p "Enter path to OFP file: " input_file
        read -p "Enter output directory: " output_dir
        echo
        echo "Extracting $input_file to $output_dir..."
        python3 ofp_extractor.py -i "$input_file" -o "$output_dir" --verbose
        ;;
    2)
        echo
        read -p "Enter path to OFP file: " input_file
        echo
        echo "Listing contents of $input_file..."
        python3 ofp_extractor.py -i "$input_file" --list-only --verbose
        ;;
    3)
        echo
        read -p "Enter directory containing OFP files: " input_dir
        read -p "Enter output directory: " output_dir
        echo
        echo "Processing all OFP files in $input_dir..."
        python3 ofp_extractor.py --batch "$input_dir" -o "$output_dir" --verbose
        ;;
    4)
        echo
        python3 ofp_extractor.py --help
        ;;
    5)
        echo "Goodbye!"
        exit 0
        ;;
    *)
        echo "Invalid choice. Please try again."
        exit 1
        ;;
esac

echo
echo "Operation completed."
read -p "Press Enter to continue..."
