# OFP Firmware Extractor Requirements
# Install with: pip install -r requirements.txt

# Cryptography library for AES encryption/decryption
pycryptodome>=3.15.0

# Standard library modules used (no installation required):
# - argparse (argument parsing)
# - os (file system operations)
# - sys (system operations)
# - logging (logging functionality)
# - hashlib (hash calculations)
# - struct (binary data parsing)
# - zipfile (ZIP file handling)
# - zlib (zlib compression)
# - lzma (LZMA compression)
# - gzip (gzip compression)
# - pathlib (path operations)
# - typing (type hints)
# - binascii (binary/ASCII conversions)
# - enum (enumerations)
# - xml.etree.ElementTree (XML parsing)
# - shutil (disk usage checking)
