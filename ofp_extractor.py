#!/usr/bin/env python3
"""
OFP Firmware Extractor
A Python command-line tool for extracting .ofp firmware files used by Realme, Oppo, and Vivo devices.

Author: AI Assistant
License: MIT
"""

import argparse
import os
import sys
import logging
import hashlib
import struct
import zipfile
import zlib
import lzma
import gzip
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any
from binascii import unhexlify, hexlify
from enum import Enum

# Version information
__version__ = "1.0.0"
__author__ = "AI Assistant"

class OFPFormat(Enum):
    """Enumeration of supported OFP format types."""
    UNKNOWN = "unknown"
    ZIP = "zip"
    QC = "qualcomm"
    MTK = "mediatek"

class OFPHeader:
    """Class to represent OFP file header information."""

    def __init__(self):
        self.format_type = OFPFormat.UNKNOWN
        self.project_name = ""
        self.cpu_type = ""
        self.flash_type = ""
        self.file_count = 0
        self.page_size = 0
        self.header_size = 0
        self.file_entries = []

class OFPExtractor:
    """Main class for OFP firmware extraction."""
    
    def __init__(self, verbose: bool = False):
        """Initialize the OFP extractor with logging configuration."""
        self.verbose = verbose
        self.setup_logging()
        
    def setup_logging(self):
        """Configure logging based on verbosity level."""
        level = logging.DEBUG if self.verbose else logging.INFO
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        self.logger = logging.getLogger(__name__)

    def detect_ofp_format(self, filepath: str) -> OFPFormat:
        """Detect the format type of an OFP file."""
        try:
            with open(filepath, 'rb') as f:
                # Check for ZIP format (PK header)
                magic = f.read(2)
                if magic == b"PK":
                    self.logger.debug("Detected ZIP format OFP file")
                    return OFPFormat.ZIP

                # Check file size for header analysis
                f.seek(0, 2)  # Seek to end
                file_size = f.tell()

                if file_size < 0x1000:
                    self.logger.warning("File too small to be a valid OFP file")
                    return OFPFormat.UNKNOWN

                # Check for MTK format by looking for header at end of file
                for header_size in [0x6C, 0x200, 0x1000]:
                    if file_size < header_size:
                        continue

                    f.seek(file_size - header_size)
                    header_data = f.read(header_size)

                    # Try to detect MTK format by looking for project name patterns
                    if self._is_mtk_header(header_data):
                        self.logger.debug("Detected MTK format OFP file")
                        return OFPFormat.MTK

                # Check for QC format by looking for XML metadata
                if self._has_qc_xml_metadata(f, file_size):
                    self.logger.debug("Detected Qualcomm format OFP file")
                    return OFPFormat.QC

                self.logger.warning("Could not determine OFP format")
                return OFPFormat.UNKNOWN

        except Exception as e:
            self.logger.error(f"Error detecting OFP format: {e}")
            return OFPFormat.UNKNOWN

    def _is_mtk_header(self, header_data: bytes) -> bool:
        """Check if header data appears to be MTK format."""
        try:
            # MTK headers typically contain project names and specific patterns
            # Look for common MTK project name patterns
            header_str = header_data.decode('utf-8', errors='ignore')
            mtk_indicators = ['CPH', 'RMX', 'OPPO', 'REALME', 'VIVO']

            for indicator in mtk_indicators:
                if indicator in header_str.upper():
                    return True

            # Check for MTK-specific binary patterns
            if len(header_data) >= 0x6C:
                # Try to parse as MTK header structure
                try:
                    # MTK header has specific structure with project name at start
                    project_name = header_data[:46].replace(b'\x00', b'').decode('utf-8', errors='ignore')
                    if len(project_name) > 0 and project_name.isprintable():
                        return True
                except:
                    pass

            return False
        except:
            return False

    def _has_qc_xml_metadata(self, f, file_size: int) -> bool:
        """Check if file contains Qualcomm XML metadata."""
        try:
            # QC format typically has XML metadata near the end
            for page_size in [0x200, 0x1000]:
                if file_size < page_size + 0x10:
                    continue

                f.seek(file_size - page_size + 0x10)
                data = f.read(min(0x1000, page_size - 0x10))

                # Look for XML patterns
                if b'<?xml' in data or b'<data>' in data or b'<Provision>' in data:
                    return True

            return False
        except:
            return False

    def _swap_nibbles(self, ch: int) -> int:
        """Swap nibbles in a byte (used in key shuffling)."""
        return ((ch & 0xF) << 4) + ((ch & 0xF0) >> 4)

    def _key_shuffle(self, key: bytearray, hkey: bytearray) -> bytearray:
        """Shuffle key using hardware key (QC format)."""
        for i in range(0, 0x10, 4):
            key[i] = self._swap_nibbles((hkey[i] ^ key[i]))
            key[i + 1] = self._swap_nibbles(hkey[i + 1] ^ key[i + 1])
            key[i + 2] = self._swap_nibbles(hkey[i + 2] ^ key[i + 2])
            key[i + 3] = self._swap_nibbles(hkey[i + 3] ^ key[i + 3])
        return key

    def _rol(self, x: int, n: int, bits: int = 32) -> int:
        """Rotate left operation."""
        n = bits - n
        mask = (2**n) - 1
        mask_bits = x & mask
        return (x >> n) | (mask_bits << (bits - n))

    def _deobfuscate(self, data: bytes, mask: bytes) -> bytearray:
        """Deobfuscate data using mask (QC format)."""
        ret = bytearray()
        for i in range(len(data)):
            v = self._rol((data[i] ^ mask[i]), 4, 8)
            ret.append(v)
        return ret

    def _mtk_shuffle(self, key: bytes, input_data: bytearray) -> bytearray:
        """MTK-specific key shuffling algorithm."""
        key_length = len(key)
        input_length = len(input_data)

        for i in range(input_length):
            k = key[i % key_length]
            h = ((((input_data[i]) & 0xF0) >> 4) | (16 * ((input_data[i]) & 0xF)))
            input_data[i] = k ^ h
        return input_data

    def _mtk_shuffle2(self, key: bytes, input_data: bytearray) -> bytearray:
        """MTK-specific key shuffling algorithm variant 2."""
        key_length = len(key)
        input_length = len(input_data)

        for i in range(input_length):
            tmp = key[i % key_length] ^ input_data[i]
            input_data[i] = ((tmp & 0xF0) >> 4) | (16 * (tmp & 0xF))
        return input_data

    def get_qc_keys(self) -> List[Tuple[str, bytes, bytes]]:
        """Get list of known QC encryption keys."""
        keys = [
            # R9s/A57t
            ["V1.4.17/1.4.27", "27827963787265EF89D126B69A495A21", "82C50203285A2CE7D8C3E198383CE94C", "422DD5399181E223813CD8ECDF2E4D72"],
            # a3s
            ["V1.6.17", "E11AA7BB558A436A8375FD15DDD4651F", "77DDF6A0696841F6B74782C097835169", "A739742384A44E8BA45207AD5C3700EA"],
            ["V1.5.13", "67657963787565E837D226B69A495D21", "F6C50203515A2CE7D8C3E1F938B7E94C", "42F2D5399137E2B2813CD8ECDF2F4D72"],
            # R15 Pro CPH1831 V1.6.6 / FindX CPH1871 V1.6.9 / R17 Pro CPH1877 V1.6.17 / R17 PBEM00 V1.6.17 / A5 2020 V1.7.6 / K3 CPH1955 V1.6.26 UFS
            ["V1.6.6/1.6.9/1.6.17/1.6.24/1.6.26/1.7.6", "3C2D518D9BF2E4279DC758CD535147C3", "87C74A29709AC1BF2382276C4E8DF232", "598D92E967265E9BCABE2469FE4A915E"],
            # RM1921EX V1.7.2, Realme X RMX1901 V1.7.2, Realme 5 Pro RMX1971 V1.7.2, Realme 5 RMX1911 V1.7.2
            ["V1.7.2", "8FB8FB261930260BE945B841AEFA9FD4", "E529E82B28F5A2F8831D860AE39E425D", "8A09DA60ED36F125D64709973372C1CF"],
            # OW19W8AP_11_A.23_200715
            ["V2.0.3", "E8AE288C0192C54BF10C5707E9C4705B", "D64FC385DCD52A3C9B5FBA8650F92EDA", "79051FD8D8B6297E2E4559E997F63B7F"]
        ]

        processed_keys = []
        for key_info in keys:
            version = key_info[0]
            mc = bytearray.fromhex(key_info[1])
            userkey = bytearray.fromhex(key_info[2])
            ivec = bytearray.fromhex(key_info[3])

            # Generate AES key and IV
            key_bytes = hashlib.md5(self._deobfuscate(userkey, mc)).hexdigest()[:16].encode()
            iv_bytes = hashlib.md5(self._deobfuscate(ivec, mc)).hexdigest()[:16].encode()

            processed_keys.append((version, key_bytes, iv_bytes))

        return processed_keys

    def get_mtk_keys(self) -> List[Tuple[str, bytes, bytes]]:
        """Get list of known MTK encryption keys."""
        key_tables = [
            ["67657963787565E837D226B69A495D21", "F6C50203515A2CE7D8C3E1F938B7E94C", "42F2D5399137E2B2813CD8ECDF2F4D72"],
            ["9E4F32639D21357D37D226B69A495D21", "A3D8D358E42F5A9E931DD3917D9A3218", "386935399137416B67416BECF22F519A"],
            ["892D57E92A4D8A975E3C216B7C9DE189", "D26DF2D9913785B145D18C7219B89F26", "516989E4A1BFC78B365C6BC57D944391"],
            ["27827963787265EF89D126B69A495A21", "82C50203285A2CE7D8C3E198383CE94C", "422DD5399181E223813CD8ECDF2E4D72"],
            ["3C4A618D9BF2E4279DC758CD535147C3", "87B13D29709AC1BF2382276C4E8DF232", "59B7A8E967265E9BCABE2469FE4A915E"],
            ["1C3288822BF824259DC852C1733127D3", "E7918D22799181CF2312176C9E2DF298", "3247F889A7B6DECBCA3E28693E4AAAFE"],
            ["1E4F32239D65A57D37D2266D9A775D43", "A332D3C3E42F5A3E931DD991729A321D", "3F2A35399A373377674155ECF28FD19A"],
            ["122D57E92A518AFF5E3C786B7C34E189", "DD6DF2D9543785674522717219989FB0", "12698965A132C76136CC88C5DD94EE91"],
            ["ab3f76d7989207f2", "2bf515b3a9737835"]  # Direct AES key/IV pair
        ]

        processed_keys = []
        for i, kt in enumerate(key_tables):
            if len(kt) == 3:
                obskey = bytearray(unhexlify(kt[0]))
                encaeskey = bytearray(unhexlify(kt[1]))
                encaesiv = bytearray(unhexlify(kt[2]))

                aeskey = hexlify(hashlib.md5(self._mtk_shuffle2(obskey, encaeskey)).digest())[:16]
                aesiv = hexlify(hashlib.md5(self._mtk_shuffle2(obskey, encaesiv)).digest())[:16]
            else:
                aeskey = kt[0].encode('utf-8')
                aesiv = kt[1].encode('utf-8')

            processed_keys.append((f"MTK_KEY_{i}", aeskey, aesiv))

        return processed_keys

    def extract_zip_ofp(self, filepath: str, output_dir: str, list_only: bool = False) -> bool:
        """Extract ZIP format OFP file."""
        try:
            # Known password for Realme/Oppo ZIP files
            zip_password = b"flash@realme$50E7F7D847732396F1582CD62DD385ED7ABB0897"

            with zipfile.ZipFile(filepath) as zf:
                file_list = zf.namelist()
                self.logger.info(f"Found {len(file_list)} files in ZIP archive")

                if list_only:
                    for filename in file_list:
                        file_info = zf.getinfo(filename)
                        self.logger.info(f"  {filename} ({file_info.file_size} bytes)")
                    return True

                # Extract files
                for filename in file_list:
                    self.logger.info(f"Extracting {filename}")
                    try:
                        zf.extract(filename, path=output_dir, pwd=zip_password)
                    except RuntimeError as e:
                        if "Bad password" in str(e):
                            self.logger.warning(f"Password protected file, trying without password: {filename}")
                            zf.extract(filename, path=output_dir)
                        else:
                            raise

                return True

        except Exception as e:
            self.logger.error(f"Error extracting ZIP OFP: {e}")
            return False

    def parse_qc_header(self, filepath: str) -> Optional[OFPHeader]:
        """Parse Qualcomm format OFP header."""
        try:
            file_size = os.path.getsize(filepath)

            # Try different page sizes and key combinations
            for page_size in [0x200, 0x1000]:
                if file_size < page_size + 0x10:
                    continue

                for version, key, iv in self.get_qc_keys():
                    try:
                        xml_data = self._extract_qc_xml(filepath, file_size, page_size, key, iv)
                        if xml_data:
                            header = self._parse_qc_xml(xml_data, page_size)
                            if header:
                                header.format_type = OFPFormat.QC
                                self.logger.info(f"Successfully parsed QC header with key version: {version}")
                                return header
                    except Exception as e:
                        self.logger.debug(f"Failed to parse with key {version}: {e}")
                        continue

            self.logger.error("Could not parse QC header with any known key")
            return None

        except Exception as e:
            self.logger.error(f"Error parsing QC header: {e}")
            return None

    def _extract_qc_xml(self, filepath: str, file_size: int, page_size: int, key: bytes, iv: bytes) -> Optional[str]:
        """Extract XML metadata from QC format OFP."""
        try:
            with open(filepath, 'rb') as f:
                f.seek(file_size - page_size + 0x10)
                encrypted_data = f.read(page_size - 0x10)

                # Decrypt using AES CFB mode
                try:
                    from Crypto.Cipher import AES
                except ImportError:
                    # Fallback to pycryptodome
                    from Cryptodome.Cipher import AES
                cipher = AES.new(key, AES.MODE_CFB, IV=iv, segment_size=128)
                decrypted_data = cipher.decrypt(encrypted_data)

                # Look for XML content
                xml_end = decrypted_data.find(b">")
                if xml_end != -1:
                    xml_data = decrypted_data[:xml_end + 1].decode('utf-8', errors='ignore')
                    if '<?xml' in xml_data or '<data>' in xml_data:
                        return xml_data

            return None

        except Exception as e:
            self.logger.debug(f"Error extracting QC XML: {e}")
            return None

    def _parse_qc_xml(self, xml_data: str, page_size: int) -> Optional[OFPHeader]:
        """Parse QC XML metadata to extract file information."""
        try:
            import xml.etree.ElementTree as ET

            root = ET.fromstring(xml_data)
            header = OFPHeader()
            header.page_size = page_size

            # Parse file entries from XML
            for child in root:
                for item in child:
                    if "Path" not in item.attrib and "filename" not in item.attrib:
                        # Handle nested items
                        for subitem in item:
                            entry = self._parse_qc_file_entry(subitem, page_size)
                            if entry:
                                header.file_entries.append(entry)
                    else:
                        entry = self._parse_qc_file_entry(item, page_size)
                        if entry:
                            header.file_entries.append(entry)

            header.file_count = len(header.file_entries)
            self.logger.info(f"Parsed {header.file_count} file entries from QC XML")
            return header

        except Exception as e:
            self.logger.error(f"Error parsing QC XML: {e}")
            return None

    def _parse_qc_file_entry(self, item, page_size: int) -> Optional[Dict[str, Any]]:
        """Parse a single QC file entry from XML."""
        try:
            entry = {}

            # Extract filename
            if "Path" in item.attrib:
                entry["filename"] = item.attrib["Path"]
            elif "filename" in item.attrib:
                entry["filename"] = item.attrib["filename"]
            else:
                return None

            # Extract checksums
            entry["sha256"] = item.attrib.get("sha256", "")
            entry["md5"] = item.attrib.get("md5", "")

            # Extract file offset and size
            if "FileOffsetInSrc" in item.attrib:
                entry["start"] = int(item.attrib["FileOffsetInSrc"]) * page_size
            elif "SizeInSectorInSrc" in item.attrib:
                entry["start"] = int(item.attrib["SizeInSectorInSrc"]) * page_size
            else:
                return None

            if "SizeInByteInSrc" in item.attrib:
                entry["real_length"] = int(item.attrib["SizeInByteInSrc"])
            else:
                entry["real_length"] = 0

            if "SizeInSectorInSrc" in item.attrib:
                entry["length"] = int(item.attrib["SizeInSectorInSrc"]) * page_size
            else:
                entry["length"] = entry["real_length"]

            return entry

        except Exception as e:
            self.logger.debug(f"Error parsing QC file entry: {e}")
            return None

    def parse_mtk_header(self, filepath: str) -> Optional[OFPHeader]:
        """Parse MediaTek format OFP header."""
        try:
            file_size = os.path.getsize(filepath)
            hdr_key = bytearray(b"geyixue")
            hdr_length = 0x6C

            if file_size < hdr_length:
                return None

            with open(filepath, 'rb') as f:
                # Try to find the correct key
                aes_key, aes_iv = self._brute_force_mtk_key(f)
                if not aes_key:
                    self.logger.error("Could not find valid MTK key")
                    return None

                # Read and decrypt main header
                f.seek(file_size - hdr_length)
                hdr_data = bytearray(f.read(hdr_length))
                hdr_data = self._mtk_shuffle(hdr_key, hdr_data)

                # Parse header structure
                header_format = "46s Q 4s 7s 5s H 32s H"
                parsed = struct.unpack(header_format, hdr_data)

                prj_name = self._clean_cstring(parsed[0])
                # unknown_val = parsed[1]  # Reserved field
                # reserved = parsed[2]     # Reserved field
                cpu = self._clean_cstring(parsed[3])
                flash_type = self._clean_cstring(parsed[4])
                hdr2_entries = parsed[5]
                # prj_info = self._clean_cstring(parsed[6])  # Additional project info
                # crc = parsed[7]          # Header CRC

                # Create header object
                header = OFPHeader()
                header.format_type = OFPFormat.MTK
                header.project_name = prj_name
                header.cpu_type = cpu
                header.flash_type = flash_type
                header.file_count = hdr2_entries

                self.logger.info(f"MTK Project: {prj_name}, CPU: {cpu}, Flash: {flash_type}")

                # Read file entries
                hdr2_length = hdr2_entries * 0x60
                if file_size < hdr2_length + hdr_length:
                    self.logger.error("File too small for MTK header entries")
                    return None

                f.seek(file_size - hdr2_length - hdr_length)
                hdr2_data = bytearray(f.read(hdr2_length))
                hdr2_data = self._mtk_shuffle(hdr_key, hdr2_data)

                # Parse file entries
                for i in range(hdr2_entries):
                    entry_offset = i * 0x60
                    entry_data = hdr2_data[entry_offset:entry_offset + 0x60]

                    entry_format = "<32s Q Q Q 32s Q"
                    parsed_entry = struct.unpack(entry_format, entry_data)

                    name = self._clean_cstring(parsed_entry[0])
                    start = parsed_entry[1]
                    length = parsed_entry[2]
                    enc_length = parsed_entry[3]
                    filename = self._clean_cstring(parsed_entry[4])
                    crc = parsed_entry[5]

                    entry = {
                        "name": name,
                        "filename": filename,
                        "start": start,
                        "length": length,
                        "enc_length": enc_length,
                        "crc": crc,
                        "aes_key": aes_key,
                        "aes_iv": aes_iv
                    }

                    header.file_entries.append(entry)

                return header

        except Exception as e:
            self.logger.error(f"Error parsing MTK header: {e}")
            return None

    def _clean_cstring(self, data: bytes) -> str:
        """Clean C-style string by removing null bytes."""
        return data.replace(b'\x00', b'').decode('utf-8', errors='ignore')

    def _brute_force_mtk_key(self, f) -> Tuple[Optional[bytes], Optional[bytes]]:
        """Brute force MTK encryption key."""
        try:
            f.seek(0)
            enc_data = f.read(16)

            for version, aes_key, aes_iv in self.get_mtk_keys():
                try:
                    # Test decryption
                    from Crypto.Cipher import AES
                except ImportError:
                    from Cryptodome.Cipher import AES

                cipher = AES.new(aes_key, AES.MODE_CFB, IV=aes_iv, segment_size=128)
                test_data = cipher.decrypt(enc_data)

                # MTK files typically start with "MMM"
                if test_data[:3] == b"MMM":
                    self.logger.info(f"Found valid MTK key: {version}")
                    return aes_key, aes_iv

            return None, None

        except Exception as e:
            self.logger.debug(f"Error in MTK key brute force: {e}")
            return None, None

    def extract_ofp(self, filepath: str, output_dir: str, list_only: bool = False) -> bool:
        """Main extraction method for OFP files."""
        try:
            self.logger.info(f"Processing OFP file: {filepath}")

            # Detect format
            format_type = self.detect_ofp_format(filepath)

            if format_type == OFPFormat.ZIP:
                return self.extract_zip_ofp(filepath, output_dir, list_only)

            elif format_type == OFPFormat.QC:
                header = self.parse_qc_header(filepath)
                if not header:
                    return False
                return self._extract_qc_files(filepath, header, output_dir, list_only)

            elif format_type == OFPFormat.MTK:
                header = self.parse_mtk_header(filepath)
                if not header:
                    return False
                return self._extract_mtk_files(filepath, header, output_dir, list_only)

            else:
                self.logger.error("Unknown or unsupported OFP format")
                return False

        except Exception as e:
            self.logger.error(f"Error extracting OFP file: {e}")
            return False

    def _extract_qc_files(self, filepath: str, header: OFPHeader, output_dir: str, list_only: bool) -> bool:
        """Extract files from Qualcomm format OFP."""
        try:
            if list_only:
                self.logger.info(f"QC OFP contains {header.file_count} files:")
                for entry in header.file_entries:
                    size_info = f"({entry['real_length']} bytes)" if entry['real_length'] else f"({entry['length']} bytes)"
                    self.logger.info(f"  {entry['filename']} {size_info}")
                return True

            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Save XML metadata (if we have it)
            # Note: XML data would need to be passed from the parsing function
            # This is a placeholder for future enhancement

            # Extract files
            success_count = 0
            total_files = len(header.file_entries)
            for i, entry in enumerate(header.file_entries, 1):
                self.logger.info(f"Progress: {i}/{total_files} files")
                if self._extract_qc_file(filepath, entry, output_dir):
                    success_count += 1

            self.logger.info(f"Successfully extracted {success_count}/{header.file_count} files")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"Error extracting QC files: {e}")
            return False

    def _extract_mtk_files(self, filepath: str, header: OFPHeader, output_dir: str, list_only: bool) -> bool:
        """Extract files from MediaTek format OFP."""
        try:
            if list_only:
                self.logger.info(f"MTK OFP contains {header.file_count} files:")
                for entry in header.file_entries:
                    size_info = f"({entry['length']} bytes)"
                    if entry['enc_length'] > 0:
                        size_info += f" [encrypted: {entry['enc_length']} bytes]"
                    self.logger.info(f"  {entry['filename']} {size_info}")
                return True

            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Extract files
            success_count = 0
            total_files = len(header.file_entries)
            for i, entry in enumerate(header.file_entries, 1):
                self.logger.info(f"Progress: {i}/{total_files} files")
                if self._extract_mtk_file(filepath, entry, output_dir):
                    success_count += 1

            self.logger.info(f"Successfully extracted {success_count}/{header.file_count} files")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"Error extracting MTK files: {e}")
            return False

    def _extract_qc_file(self, filepath: str, entry: Dict[str, Any], output_dir: str) -> bool:
        """Extract a single file from QC format OFP."""
        try:
            output_path = os.path.join(output_dir, entry['filename'])

            # Check available disk space
            if not self._check_disk_space(output_dir, entry.get('real_length', entry.get('length', 0))):
                return False

            try:
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
            except PermissionError:
                self.logger.error(f"Permission denied creating directory for {entry['filename']}")
                return False

            self.logger.info(f"Extracting {entry['filename']}")

            with open(filepath, 'rb') as src:
                with open(output_path, 'wb') as dst:
                    src.seek(entry['start'])

                    # Copy file data
                    remaining = entry['real_length'] if entry['real_length'] > 0 else entry['length']
                    while remaining > 0:
                        chunk_size = min(0x100000, remaining)  # 1MB chunks
                        data = src.read(chunk_size)
                        if not data:
                            break
                        dst.write(data)
                        remaining -= len(data)

            # Verify checksums if available
            self._verify_file_integrity(output_path, entry.get('md5', ''), entry.get('sha256', ''))

            # Try to decompress if it looks compressed
            self._try_decompress_file(output_path)

            return True

        except Exception as e:
            self.logger.error(f"Error extracting QC file {entry['filename']}: {e}")
            return False

    def _extract_mtk_file(self, filepath: str, entry: Dict[str, Any], output_dir: str) -> bool:
        """Extract a single file from MTK format OFP."""
        try:
            output_path = os.path.join(output_dir, entry['filename'])

            # Check available disk space
            if not self._check_disk_space(output_dir, entry.get('length', 0)):
                return False

            try:
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
            except PermissionError:
                self.logger.error(f"Permission denied creating directory for {entry['filename']}")
                return False

            self.logger.info(f"Extracting {entry['name']} as {entry['filename']}")

            with open(filepath, 'rb') as src:
                with open(output_path, 'wb') as dst:
                    src.seek(entry['start'])

                    # Handle encrypted portion
                    if entry['enc_length'] > 0:
                        enc_data = src.read(entry['enc_length'])

                        # Pad to 16-byte boundary for AES
                        if len(enc_data) % 16 != 0:
                            enc_data += b'\x00' * (16 - (len(enc_data) % 16))

                        # Decrypt
                        try:
                            from Crypto.Cipher import AES
                        except ImportError:
                            from Cryptodome.Cipher import AES

                        cipher = AES.new(entry['aes_key'], AES.MODE_CFB, IV=entry['aes_iv'], segment_size=128)
                        dec_data = cipher.decrypt(enc_data)
                        dst.write(dec_data[:entry['enc_length']])

                        # Handle remaining unencrypted data
                        remaining = entry['length'] - entry['enc_length']
                        while remaining > 0:
                            chunk_size = min(0x200000, remaining)  # 2MB chunks
                            data = src.read(chunk_size)
                            if not data:
                                break
                            dst.write(data)
                            remaining -= len(data)
                    else:
                        # File is not encrypted
                        remaining = entry['length']
                        while remaining > 0:
                            chunk_size = min(0x200000, remaining)
                            data = src.read(chunk_size)
                            if not data:
                                break
                            dst.write(data)
                            remaining -= len(data)

            # Try to decompress if it looks compressed
            self._try_decompress_file(output_path)

            return True

        except Exception as e:
            self.logger.error(f"Error extracting MTK file {entry['filename']}: {e}")
            return False

    def _verify_file_integrity(self, filepath: str, md5_hash: str, sha256_hash: str) -> None:
        """Verify file integrity using checksums."""
        try:
            if not md5_hash and not sha256_hash:
                return

            with open(filepath, 'rb') as f:
                # For large files, only check first 256KB for performance
                file_size = os.path.getsize(filepath)
                check_size = min(file_size, 0x40000)
                data = f.read(check_size)

                if md5_hash:
                    calculated_md5 = hashlib.md5(data).hexdigest()
                    if calculated_md5.lower() == md5_hash.lower():
                        self.logger.debug(f"MD5 verification passed for {os.path.basename(filepath)}")
                    else:
                        self.logger.warning(f"MD5 verification failed for {os.path.basename(filepath)}")

                if sha256_hash:
                    if check_size == file_size:
                        # Small file, check entire content
                        calculated_sha256 = hashlib.sha256(data).hexdigest()
                    else:
                        # Large file, check first chunk only
                        calculated_sha256 = hashlib.sha256(data).hexdigest()

                    if calculated_sha256.lower() == sha256_hash.lower():
                        self.logger.debug(f"SHA256 verification passed for {os.path.basename(filepath)}")
                    else:
                        self.logger.warning(f"SHA256 verification failed for {os.path.basename(filepath)}")

        except Exception as e:
            self.logger.debug(f"Error verifying file integrity: {e}")

    def _try_decompress_file(self, filepath: str) -> bool:
        """Try to decompress a file if it appears to be compressed."""
        try:
            with open(filepath, 'rb') as f:
                header = f.read(16)
                f.seek(0)
                data = f.read()

            decompressed_data = None
            compression_type = None

            # Check for different compression formats
            if header.startswith(b'\x1f\x8b'):  # gzip
                try:
                    decompressed_data = gzip.decompress(data)
                    compression_type = "gzip"
                except:
                    pass

            elif header.startswith(b'\xfd7zXZ'):  # xz/lzma
                try:
                    decompressed_data = lzma.decompress(data)
                    compression_type = "lzma"
                except:
                    pass

            elif header.startswith(b'x\x9c') or header.startswith(b'x\x01') or header.startswith(b'x\xda'):  # zlib
                try:
                    decompressed_data = zlib.decompress(data)
                    compression_type = "zlib"
                except:
                    pass

            # If decompression was successful, save the decompressed file
            if decompressed_data and len(decompressed_data) > len(data):
                decompressed_path = filepath + '.decompressed'
                with open(decompressed_path, 'wb') as f:
                    f.write(decompressed_data)

                self.logger.info(f"Decompressed {compression_type} file: {os.path.basename(filepath)} -> {os.path.basename(decompressed_path)}")
                return True

            return False

        except Exception as e:
            self.logger.debug(f"Error trying to decompress file {filepath}: {e}")
            return False

    def _check_disk_space(self, output_dir: str, required_bytes: int) -> bool:
        """Check if there's enough disk space for extraction."""
        try:
            import shutil
            free_bytes = shutil.disk_usage(output_dir).free

            # Add 10% buffer for safety
            required_with_buffer = int(required_bytes * 1.1)

            if free_bytes < required_with_buffer:
                self.logger.error(f"Insufficient disk space. Required: {required_with_buffer:,} bytes, Available: {free_bytes:,} bytes")
                return False

            return True

        except Exception as e:
            self.logger.warning(f"Could not check disk space: {e}")
            return True  # Assume it's okay if we can't check

def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        prog='ofp_extractor',
        description='Extract .ofp firmware files used by Realme, Oppo, and Vivo devices',
        epilog='''
Examples:
  %(prog)s -i firmware.ofp -o extracted/
  %(prog)s -i firmware.ofp -o extracted/ --verbose
  %(prog)s -i firmware.ofp --list-only
  %(prog)s --batch firmware_dir/ -o extracted_dir/
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Required arguments
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        '-i', '--input',
        type=str,
        help='Input .ofp firmware file to extract'
    )
    input_group.add_argument(
        '--batch',
        type=str,
        metavar='DIR',
        help='Process all .ofp files in the specified directory'
    )
    
    # Output directory (required unless --list-only is used)
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='Output directory for extracted files (required unless --list-only is used)'
    )
    
    # Optional flags
    parser.add_argument(
        '--list-only',
        action='store_true',
        help='List contents of .ofp file without extracting'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging output'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version=f'%(prog)s {__version__}'
    )
    
    return parser

def validate_arguments(args: argparse.Namespace) -> None:
    """Validate command line arguments and check file/directory existence."""
    # Check if output is required
    if not args.list_only and not args.output:
        raise argparse.ArgumentTypeError("Output directory (-o/--output) is required unless --list-only is used")
    
    # Validate input file or batch directory
    if args.input:
        if not os.path.isfile(args.input):
            raise FileNotFoundError(f"Input file not found: {args.input}")
        if not args.input.lower().endswith('.ofp'):
            logging.warning(f"Input file does not have .ofp extension: {args.input}")
    
    if args.batch:
        if not os.path.isdir(args.batch):
            raise NotADirectoryError(f"Batch directory not found: {args.batch}")
        
        # Check if directory contains .ofp files
        ofp_files = list(Path(args.batch).glob('*.ofp'))
        if not ofp_files:
            raise ValueError(f"No .ofp files found in batch directory: {args.batch}")
        logging.info(f"Found {len(ofp_files)} .ofp files in batch directory")
    
    # Validate output directory
    if args.output:
        output_path = Path(args.output)
        if output_path.exists() and not output_path.is_dir():
            raise NotADirectoryError(f"Output path exists but is not a directory: {args.output}")
        
        # Check if we can create the output directory
        try:
            output_path.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            raise PermissionError(f"Cannot create output directory (permission denied): {args.output}")
        except OSError as e:
            raise OSError(f"Cannot create output directory: {args.output} - {e}")

def main():
    """Main entry point for the OFP extractor."""
    parser = create_argument_parser()
    
    try:
        args = parser.parse_args()
        validate_arguments(args)
        
        # Initialize extractor
        extractor = OFPExtractor(verbose=args.verbose)
        
        if args.list_only:
            logging.info("List-only mode enabled - no files will be extracted")
        
        # Process files
        success = True
        if args.input:
            # Single file processing
            logging.info(f"Processing single file: {args.input}")
            success = extractor.extract_ofp(args.input, args.output or "", args.list_only)

        elif args.batch:
            # Batch processing
            logging.info(f"Processing batch directory: {args.batch}")
            ofp_files = list(Path(args.batch).glob('*.ofp'))
            success_count = 0

            for ofp_file in ofp_files:
                logging.info(f"Processing {ofp_file.name}")
                if args.output:
                    file_output_dir = os.path.join(args.output, ofp_file.stem)
                else:
                    file_output_dir = ""

                if extractor.extract_ofp(str(ofp_file), file_output_dir, args.list_only):
                    success_count += 1
                else:
                    logging.error(f"Failed to process {ofp_file.name}")

            success = success_count == len(ofp_files)
            logging.info(f"Batch processing completed: {success_count}/{len(ofp_files)} files processed successfully")

        if success:
            logging.info("Processing completed successfully")
            return 0
        else:
            logging.error("Processing completed with errors")
            return 1
        
    except KeyboardInterrupt:
        logging.error("Operation cancelled by user")
        return 130
    except (FileNotFoundError, NotADirectoryError, PermissionError, ValueError, OSError) as e:
        logging.error(f"Error: {e}")
        return 1
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        if args.verbose if 'args' in locals() else False:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
