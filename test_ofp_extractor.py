#!/usr/bin/env python3
"""
Test script for OFP Extractor
Tests basic functionality without requiring actual .ofp files
"""

import os
import sys
import tempfile
import zipfile
from pathlib import Path

# Add the current directory to Python path to import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ofp_extractor import OFPExtractor, OFPFormat, create_argument_parser

def test_argument_parser():
    """Test the argument parser."""
    print("Testing argument parser...")
    
    parser = create_argument_parser()
    
    # Test help (this should work)
    try:
        args = parser.parse_args(['--help'])
    except SystemExit:
        print("✓ Help argument works correctly")
    
    # Test version
    try:
        args = parser.parse_args(['--version'])
    except SystemExit:
        print("✓ Version argument works correctly")
    
    print("✓ Argument parser tests passed")

def test_extractor_initialization():
    """Test OFP extractor initialization."""
    print("Testing extractor initialization...")
    
    # Test normal initialization
    extractor = OFPExtractor(verbose=False)
    assert extractor is not None
    print("✓ Normal initialization works")
    
    # Test verbose initialization
    extractor_verbose = OFPExtractor(verbose=True)
    assert extractor_verbose is not None
    print("✓ Verbose initialization works")
    
    print("✓ Extractor initialization tests passed")

def test_format_detection():
    """Test format detection with mock files."""
    print("Testing format detection...")
    
    extractor = OFPExtractor(verbose=False)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test ZIP format detection
        zip_file = os.path.join(temp_dir, "test.ofp")
        with zipfile.ZipFile(zip_file, 'w') as zf:
            zf.writestr("test.txt", "test content")
        
        format_type = extractor.detect_ofp_format(zip_file)
        assert format_type == OFPFormat.ZIP
        print("✓ ZIP format detection works")
        
        # Test unknown format (empty file)
        empty_file = os.path.join(temp_dir, "empty.ofp")
        with open(empty_file, 'wb') as f:
            f.write(b"")
        
        format_type = extractor.detect_ofp_format(empty_file)
        assert format_type == OFPFormat.UNKNOWN
        print("✓ Unknown format detection works")
    
    print("✓ Format detection tests passed")

def test_utility_functions():
    """Test utility functions."""
    print("Testing utility functions...")
    
    extractor = OFPExtractor(verbose=False)
    
    # Test nibble swapping
    result = extractor._swap_nibbles(0x12)
    assert result == 0x21
    print("✓ Nibble swapping works")
    
    # Test string cleaning
    test_bytes = b"test\x00\x00\x00string\x00"
    result = extractor._clean_cstring(test_bytes)
    assert result == "teststring"
    print("✓ C-string cleaning works")
    
    # Test key generation functions
    qc_keys = extractor.get_qc_keys()
    assert len(qc_keys) > 0
    print(f"✓ QC keys loaded: {len(qc_keys)} key sets")
    
    mtk_keys = extractor.get_mtk_keys()
    assert len(mtk_keys) > 0
    print(f"✓ MTK keys loaded: {len(mtk_keys)} key sets")
    
    print("✓ Utility function tests passed")

def test_compression_detection():
    """Test compression detection."""
    print("Testing compression detection...")
    
    extractor = OFPExtractor(verbose=False)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a test file with gzip header
        gzip_file = os.path.join(temp_dir, "test.gz")
        with open(gzip_file, 'wb') as f:
            f.write(b'\x1f\x8b\x08\x00\x00\x00\x00\x00\x00\x03')  # gzip header
            f.write(b'some compressed data here')
        
        # This should detect as potentially compressed but not actually decompress
        # since it's not valid gzip data
        result = extractor._try_decompress_file(gzip_file)
        print(f"✓ Compression detection attempted (result: {result})")
    
    print("✓ Compression detection tests passed")

def test_error_handling():
    """Test error handling."""
    print("Testing error handling...")
    
    extractor = OFPExtractor(verbose=False)
    
    # Test with non-existent file
    format_type = extractor.detect_ofp_format("non_existent_file.ofp")
    assert format_type == OFPFormat.UNKNOWN
    print("✓ Non-existent file handling works")
    
    # Test extraction with invalid file
    result = extractor.extract_ofp("non_existent_file.ofp", "/tmp", list_only=True)
    assert result == False
    print("✓ Invalid file extraction handling works")
    
    print("✓ Error handling tests passed")

def main():
    """Run all tests."""
    print("=" * 50)
    print("OFP Extractor Test Suite")
    print("=" * 50)
    
    try:
        test_argument_parser()
        print()
        
        test_extractor_initialization()
        print()
        
        test_format_detection()
        print()
        
        test_utility_functions()
        print()
        
        test_compression_detection()
        print()
        
        test_error_handling()
        print()
        
        print("=" * 50)
        print("✓ All tests passed successfully!")
        print("=" * 50)
        
        return 0
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
