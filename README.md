# OFP Firmware Extractor

A Python command-line tool for extracting .ofp firmware files used by Realme, Oppo, and Vivo devices (commonly for MediaTek or Qualcomm chipsets).

## Features

- **Multi-format support**: Handles ZIP, Qualcomm (QC), and MediaTek (MTK) .ofp formats
- **Automatic format detection**: Intelligently detects the .ofp file format
- **Encryption handling**: Supports multiple encryption keys for different firmware versions
- **Compression support**: Automatically decompresses zlib, lzma, and gzip compressed files
- **Batch processing**: Process multiple .ofp files in a directory
- **List-only mode**: Preview file contents without extraction
- **File integrity verification**: MD5 and SHA256 hash verification where available
- **Progress reporting**: Shows extraction progress for large files
- **Cross-platform**: Works on Windows, Linux, and macOS

## Requirements

- Python 3.8 or higher
- Required packages:
  - `pycryptodome` (for encryption/decryption)

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install pycryptodome
   ```

## Usage

### Basic Usage

Extract a single .ofp file:
```bash
python ofp_extractor.py -i firmware.ofp -o extracted/
```

### Advanced Usage

**List contents without extracting:**
```bash
python ofp_extractor.py -i firmware.ofp --list-only
```

**Batch process multiple files:**
```bash
python ofp_extractor.py --batch firmware_directory/ -o extracted_directory/
```

**Verbose output:**
```bash
python ofp_extractor.py -i firmware.ofp -o extracted/ --verbose
```

### Command Line Options

- `-i, --input FILE`: Input .ofp firmware file to extract (required unless using --batch)
- `--batch DIR`: Process all .ofp files in the specified directory
- `-o, --output DIR`: Output directory for extracted files (required unless --list-only)
- `--list-only`: List contents of .ofp file without extracting
- `--verbose, -v`: Enable verbose logging output
- `--version`: Show version information
- `--help`: Show help message

## Supported Formats

### ZIP Format
- Password-protected ZIP files with known Realme/Oppo passwords
- Automatic password detection and fallback

### Qualcomm (QC) Format
- Multiple encryption key versions supported
- XML metadata parsing
- File integrity verification with checksums

### MediaTek (MTK) Format
- Header-based file table parsing
- Multiple encryption algorithms
- Project information extraction (CPU type, flash type, etc.)

## Output

The tool creates an organized directory structure with:
- Extracted firmware files
- Decompressed versions of compressed files (when detected)
- XML metadata files (for QC format)
- Progress and status information

## Error Handling

The tool handles various error conditions:
- **Corrupted files**: Graceful handling with clear error messages
- **Insufficient permissions**: Permission checks before extraction
- **Disk space**: Automatic disk space verification
- **Unknown formats**: Clear indication of unsupported files
- **Missing dependencies**: Helpful installation instructions

## Exit Codes

- `0`: Success
- `1`: General error (file not found, permission denied, etc.)
- `130`: Operation cancelled by user (Ctrl+C)

## Examples

### Extract a Realme firmware file:
```bash
python ofp_extractor.py -i RMX3085_11_A.12_220101.ofp -o extracted/
```

### Preview contents of multiple files:
```bash
python ofp_extractor.py --batch firmware_files/ --list-only
```

### Extract with verbose logging:
```bash
python ofp_extractor.py -i firmware.ofp -o extracted/ --verbose
```

## Limitations

- **Encrypted files**: Some encrypted files may not be extractable without proper keys
- **New formats**: Newer .ofp format variants may not be supported
- **Large files**: Very large firmware files may require significant disk space and time
- **Dependencies**: Requires pycryptodome for encryption support

## Troubleshooting

### Common Issues

1. **"Unknown key" error**: The firmware uses an encryption key not in our database
2. **Permission denied**: Run with appropriate permissions or change output directory
3. **Insufficient disk space**: Ensure adequate free space (tool checks automatically)
4. **Import error**: Install required dependencies with `pip install pycryptodome`

### Getting Help

If you encounter issues:
1. Run with `--verbose` flag for detailed logging
2. Check that the .ofp file is not corrupted
3. Ensure you have sufficient disk space and permissions
4. Verify Python version (3.8+ required)

## Technical Details

The tool implements:
- Format detection through magic bytes and header analysis
- Multiple encryption key databases for different firmware versions
- AES CFB mode decryption for encrypted content
- XML parsing for Qualcomm format metadata
- Binary header parsing for MediaTek format
- Automatic compression detection and decompression

## License

MIT License - See LICENSE file for details

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests.

## Acknowledgments

Based on research from:
- bkerler's oppo_decrypt project
- Android firmware extraction community
- Reverse engineering efforts by various researchers
